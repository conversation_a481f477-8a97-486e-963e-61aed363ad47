import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/complete_check_list/complete_check_list_bloc.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_activate_and_open_card/store_activate_and_open_card_bloc.dart';
import 'package:swadesic/model/store_dashboard_response/store_dashboard_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/common_methods.dart';

class CompleteCheckList extends StatefulWidget {
  // final StoreDashBoardBloc storeDashBoardBloc;
  final StoreDashBoard storeDashBoard;
  final int storeId;
  final BuildContext previousScreenContext;

  const CompleteCheckList(
      {Key? key,
      required this.storeDashBoard,
      required this.previousScreenContext,
      required this.storeId})
      : super(key: key);

  @override
  State<CompleteCheckList> createState() => _CompleteCheckListState();
}

class _CompleteCheckListState extends State<CompleteCheckList> {
  //region Bloc
  late CompleteCheckListBloc completeCheckListBloc;
  StoreDashBoard storeDashboardResponse = StoreDashBoard();
  //endregion
  //region Init
  @override
  void initState() {
    completeCheckListBloc = CompleteCheckListBloc(
        context, widget.previousScreenContext, widget.storeDashBoard);
    storeDashboardResponse = widget.storeDashBoard;
    super.initState();
  }

  //endregion
  //region Did change
  // @override
  // void didUpdateWidget(covariant CompleteCheckList oldWidget) {
  //   completeCheckListBloc = CompleteCheckListBloc(context, widget.previousScreenContext, widget.storeDashBoard);
  //   super.didUpdateWidget(oldWidget);
  // }

  //endregion
  //region Dispose
  @override
  void dispose() {
    completeCheckListBloc.dispose();
    super.dispose();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          toProvide(),
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // checkListProgress(),
              checkList(),
              activeInActiveStore(),
            ],
          ),
        ],
      ),
    );
  }

  //region To provide
  Widget toProvide() {
    return Container(
        margin: const EdgeInsets.symmetric(vertical: 20),
        child: Text(
          AppStrings.toProvideAReliableExprience,
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
        ));
  }

  //endregion

//region Check list
  Widget checkList() {
    var data = widget.storeDashBoard;
    return StreamBuilder<bool>(
        stream: completeCheckListBloc.checkListRefriesCtrl.stream,
        builder: (context, snapshot) {
          return Visibility(
            visible: completeCheckListBloc.isDropdownEnable,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ///Basic info
                checkAndName(
                    isCompleted: true,
                    checkName: AppStrings.basicInfo,
                    onTap: () {
                      Navigator.pop(context);
                      completeCheckListBloc.goToEditStore();
                    }),

                // checkAndName(
                //
                //     ///Add product
                //     isCompleted: data.addProducts!,
                //     checkName: "Add Products (min. 3 products)",
                //     onTap: () {
                //       //Close bottom sheet
                //       // Navigator.pop(context);
                //       //Go to add product
                //      completeCheckListBloc.goToAddProduct();
                //     }),

                ///Review trust center
                checkAndName(
                    isCompleted: data.trustcenterDetail!,
                    checkName: "Review Trust center",
                    onTap: () {
                      //Close bottom sheet
                      // Navigator.pop(context);
                      //Go to trust center
                      completeCheckListBloc.goToTrustCenter();
                    }),
              ],
            ),
          );
        });
  }

  //endregion

//region check status and name
  Widget checkAndName(
      {required bool isCompleted,
      required String checkName,
      required dynamic onTap}) {
    return InkWell(
      onTap: () {
        onTap();
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 20),
        alignment: Alignment.centerLeft,
        padding:
            const EdgeInsets.only(left: 30, right: 10, top: 15, bottom: 15),
        decoration: AppCommonWidgets.shadowDecoration(),
        child: Row(
          children: [
            //Text
            Expanded(
              child: Text(
                checkName,
                style: AppTextStyle.access0(textColor: AppColors.appBlack),
              ),
            ),
            //icon
            SizedBox(
              height: 24,
              width: 24,
              child: isCompleted
                  ? Image.asset(
                      AppImages.checkListDone,
                      color: AppColors.brandBlack,
                      height: 24,
                      width: 24,
                    )
                  : const SizedBox(),
            )
          ],
        ),
      ),
    );
  }

//endregion

//region Active/inactive store
  Widget activeInActiveStore() {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      disabledColor: AppColors.textFieldFill1,
      onPressed: () async {
        if (!storeDashboardResponse.trustcenterDetail!) {
          // Trust center not completed → show toast
          CommonMethods.toastMessage(
            AppStrings.pleaseCompleteTrustCenterDetails,
            context,
          );
          return;
        }

        //Check is store is active
        if(storeDashboardResponse.isActive!){
          CommonMethods.toastMessage(AppStrings.yourStoreIsAlreadyActive, context);
          Navigator.pop(context);
          return;
        }

        // Trust center completed → call API
        final storeDashboardBloc = StoreDashBoardBloc(context, widget.storeDashBoard.storeReference!);
        await storeDashboardBloc.activeDeActiveApiCall();
        Navigator.pop(context);
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
        width: double.infinity,
        decoration: BoxDecoration(
          color: storeDashboardResponse.dashboardProgress == 100
              ? AppColors.brandBlack
              : AppColors.textFieldFill1,
          borderRadius: BorderRadius.circular(60),
        ),
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(vertical: 15),
        child: Text(
          AppStrings.goLiveMakeYour,
          style: AppTextStyle.access0(
            textColor: storeDashboardResponse.dashboardProgress == 100
                ? AppColors.appWhite
                : AppColors.appBlack,
          ),
        ),
      ),
    );
  }

//endregion
}
