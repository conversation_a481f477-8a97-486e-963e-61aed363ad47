import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/modular_store_dashboard_container_bloc.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_activate_and_open_card/store_activate_and_open_card.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_valuation_widget.dart';
import 'package:swadesic/model/store_dashboard_response/store_dashboard_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class ModularStoreDashboardContainer extends StatefulWidget {
  final String storeReference;

  const ModularStoreDashboardContainer({
    Key? key,
    required this.storeReference,
  }) : super(key: key);

  @override
  State<ModularStoreDashboardContainer> createState() =>
      _ModularStoreDashboardContainerState();
}

class _ModularStoreDashboardContainerState
    extends State<ModularStoreDashboardContainer> {
  late ModularStoreDashboardContainerBloc bloc;
  PageController? _pageController;
  Timer? _autoScrollTimer;

  @override
  void initState() {
    super.initState();
    bloc = ModularStoreDashboardContainerBloc(context, widget.storeReference);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      bloc.init();
    });
  }

  @override
  void dispose() {
    _autoScrollTimer?.cancel();
    _pageController?.dispose();
    bloc.dispose();
    super.dispose();
  }

  void _initializeAutoScroll() {
    _autoScrollTimer?.cancel();
    _pageController = PageController();

    _autoScrollTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (!mounted || _pageController == null || !_pageController!.hasClients) {
        return;
      }

      final currentPage = _pageController!.page?.round() ?? 0;
      final nextPage = (currentPage + 1) % 3; // 3 pages for normal view

      _pageController!.animateToPage(
        nextPage,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    });
  }

  void _stopAutoScroll() {
    _autoScrollTimer?.cancel();
    _pageController?.dispose();
    _pageController = null;
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<ModularDashboardState>(
      stream: bloc.stateController.stream,
      builder: (context, snapshot) {
        final state = snapshot.data ?? ModularDashboardState.loading;

        if (state == ModularDashboardState.loading) {
          return _buildLoadingWidget();
        }

        if (state == ModularDashboardState.error) {
          return _buildErrorWidget();
        }

        return Consumer<StoreDashboardDataModel>(
          builder: (context, dashboardModel, _) {
            final storeDashBoard = dashboardModel.storeDashBoard;
            if (storeDashBoard == null) {
              return _buildErrorWidget();
            }
          final pages = <Widget>[];
          //Onboarding not completed, not active
          if (storeDashBoard.dashboardProgress < 100 && !storeDashBoard.isActive!) {
            pages.add(bloc.buildFinishSetupWidget(storeDashBoard));
          }
          //Onboarding completed, Activation completed, Verification not completed
          if (storeDashBoard.dashboardProgress >= 100 &&
              !storeDashBoard.isVerificationCompleted!) {
            pages.add(bloc.buildVerificationInProgressWidget());
          }
          //Onboarding completed, Activation completed, Verification completed, not open for order
          if (storeDashBoard.dashboardProgress >= 100 &&
              storeDashBoard.isActive! &&
              storeDashBoard.isVerificationCompleted! &&
              !storeDashBoard.openForOrder!) {
            pages.add(bloc.buildGetOrdersWidget(storeDashBoard));
          }

          //Onboarding completed, Activation completed, Verification completed, open for order
          if (storeDashBoard.dashboardProgress >= 100 &&
              storeDashBoard.isActive! &&
              storeDashBoard.firstVerifiedDate!.isNotEmpty
            ) {
            pages.add(bloc.buildMiniDashboardWidget(storeDashBoard));
          }

          //Onboarding completed, Activation completed, not discoverable
          if (storeDashBoard.dashboardProgress >= 100 &&
              storeDashBoard.isActive! &&
              !storeDashBoard.isDiscoverable!) {
            pages.add(bloc.buildPublicDiscoveryWidget());
          }

          pages.add(bloc.buildMakeMostOutofSwadesicWidget());
          pages.add(bloc.buildStoreValuationWidget());

          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            height: 120,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: AppColors.textFieldFill1,
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: PageView(
                controller: _pageController,
                children: pages,
              ),
            ),
          );
          },
        );
      },
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      height: 200,
      alignment: Alignment.center,
      child: AppCommonWidgets.appCircularProgress(),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      height: 200,
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('Failed to load dashboard'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              bloc.init();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildOnboardingFlow(StoreDashBoard storeDashBoard) {
    final onboardingStep = bloc.determineOnboardingStep(storeDashBoard);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 6, vertical: 5),
      padding: const EdgeInsets.all(16),
      decoration: AppCommonWidgets.shadowDecoration(),
      child: _getOnboardingWidget(onboardingStep, storeDashBoard),
    );
  }

  Widget _buildNormalViewFlow(StoreDashBoard storeDashBoard) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      height: 120, // Fixed height for consistent UI
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: AppColors.textFieldFill1,
      ),
      child: PageView(
        controller: _pageController,
        children: [
          bloc.buildMiniDashboardWidget(storeDashBoard),
          bloc.buildPublicDiscoveryWidget(),
          bloc.buildMakeMostOutofSwadesicWidget(),
          bloc.buildStoreValuationWidget(),
        ],
      ),
    );
  }

  Widget _getOnboardingWidget(
      OnboardingStep step, StoreDashBoard storeDashBoard) {
    switch (step) {
      case OnboardingStep.finishSetup:
        return bloc.buildFinishSetupWidget(storeDashBoard);
      case OnboardingStep.verificationInProgress:
        return bloc.buildVerificationInProgressWidget();
      case OnboardingStep.getOrders:
        return bloc.buildGetOrdersWidget(storeDashBoard);
    }
  }
}

enum ModularDashboardState {
  loading,
  success,
  error,
}

enum DashboardFlowType {
  onboarding,
  normalView,
}

enum OnboardingStep {
  finishSetup,
  verificationInProgress,
  getOrders,
}
