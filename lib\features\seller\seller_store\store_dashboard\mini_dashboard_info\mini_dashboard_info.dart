import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/seller_dashboard_and_rewards/seller_dashboard/our_meter/seller_dashboard_our_meter_tool_tips/more_to_get_tool_tip.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/mini_dashboard_info/mini_dashboard_bloc.dart';
import 'package:swadesic/features/widgets/app_animated_dialog/app_animated_dialog.dart';
import 'package:swadesic/features/widgets/mini_dashboard_tooltip_info/mini_dashboard_tooltip_info.dart';
import 'package:swadesic/model/store_dashboard_response/store_dashboard_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class MiniDashboard extends StatefulWidget {
  const MiniDashboard({super.key});

  @override
  State<MiniDashboard> createState() => _MiniDashboardState();
}

class _MiniDashboardState extends State<MiniDashboard> {
  //region Bloc
  late MiniDashboardBloc miniDashboardBloc;

  //endregion

  //region Init
  @override
  void initState() {
    miniDashboardBloc = MiniDashboardBloc(context);
    miniDashboardBloc.init();
    super.initState();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return StreamBuilder<MiniDashBoardState>(
        stream: miniDashboardBloc.miniDashBoardStateCtrl.stream,
        initialData: MiniDashBoardState.Loading,
        builder: (context, snapshot) {
          //Loading
          if (snapshot.data == MiniDashBoardState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          //Success
          if (snapshot.data == MiniDashBoardState.Success) {
            return Consumer<StoreDashboardDataModel>(
              builder: (BuildContext context, StoreDashboardDataModel value, Widget? child) {
                return Visibility(
                  visible: value.storeDashBoard.isActive!,
                  child: Column(
                      children: [
                        positiveFlow(storeDashBoard: value.storeDashBoard),
                        negativeFlow(storeDashBoard: value.storeDashBoard),
                      ],
                    ),
                );
              },
            );
          }
          //Failed
          return AppCommonWidgets.errorWidget(
              height: 200,
              onTap: () {
                miniDashboardBloc.init();
              });
        });
  }

//endregion

//region Positive flow
  Widget positiveFlow({required StoreDashBoard storeDashBoard}) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          imageAndTextPositiveFlow(image: AppImages.waitingForConfirmation, count: storeDashBoard.waitingForConfirmation!),
          imageAndTextPositiveFlow(image: AppImages.confirmedNotYetShipped, count: storeDashBoard.confirmedNotShipped!),
          imageAndTextPositiveFlow(image: AppImages.shippingInProgress, count: storeDashBoard.shippingInProgress!),
          imageAndTextPositiveFlow(image: AppImages.delivered, count: storeDashBoard.delivered!),
          StreamBuilder<bool>(
              stream: miniDashboardBloc.dropDownCtrl.stream,
              initialData: false,
              builder: (context, snapshot) {
                return RotatedBox(
                    quarterTurns: snapshot.data! ? 2 : 0,
                    child: imageAndTextPositiveFlow(
                        image: AppImages.arrow,
                        onTap: () {
                          miniDashboardBloc.onTapArrow(value: snapshot.data!);
                        }));
              }),
        ],
      ),
    );
  }

//endregion

  //region Positive flow
  Widget negativeFlow({required StoreDashBoard storeDashBoard}) {
    return StreamBuilder<bool>(
        stream: miniDashboardBloc.dropDownCtrl.stream,
        initialData: false,
        builder: (context, snapshot) {
          return Visibility(
            visible: snapshot.data!,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                    margin: EdgeInsets.symmetric(horizontal: MediaQuery.of(context).size.width * 0.1),
                    child: Divider(
                      color: AppColors.disableBlack,
                    )),
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 10),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      imageAndTextNegativeFlow(image: AppImages.cancelled, count: storeDashBoard.cancelled!),
                      imageAndTextNegativeFlow(image: AppImages.returnRequested, count: storeDashBoard.returnRequested!),
                      imageAndTextNegativeFlow(image: AppImages.returnPickup, count: storeDashBoard.returnToPickup!),
                      imageAndTextNegativeFlow(image: AppImages.returned, count: storeDashBoard.returned!),
                      imageAndTextNegativeFlow(image: AppImages.refunded, count: storeDashBoard.refunded!),
                      imageAndTextNegativeFlow(image: AppImages.refundHold, count: storeDashBoard.refundHold!),
                    ],
                  ),
                ),
          Align(
            alignment: Alignment.centerRight,
            child: CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: (){
              showDialog(

                context: context,

                builder: (_) => AppAnimatedDialog(child: MiniDashboardTooltipInfo()),
              );
            }, child: Text("Show status labels",style: AppTextStyle.smallText(textColor: AppColors.writingBlack1),),
                     ),
          )
              ],
            ),
          );
        });
  }

//endregion

//region Image and text positive flow
  Widget imageAndTextPositiveFlow({required String image, int? count, Function? onTap}) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () {
        if (onTap != null) {
          onTap();
        } else {
          return;
        }
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          image.endsWith('svg')
              ? SvgPicture.asset(
                  image,
                  height: 30,
                  color: AppColors.appBlack,
                )
              : Image.asset(
                  image,
                  height: 30,
                ),
          Visibility(
            visible: count != null,
            child: Container(
                margin: const EdgeInsets.only(left: 5),
                child: Text(
                  "${count}",
                  style: AppTextStyle.access1(textColor: AppColors.appBlack),
                )),
          )
        ],
      ),
    );
  }

//endregion

//region Image and text negative flow
  Widget imageAndTextNegativeFlow({required String image, int? count, Function? onTap}) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () {
        if (onTap != null) {
          onTap();
        } else {
          return;
        }
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          image.endsWith('svg')
              ? SvgPicture.asset(
                  image,
                  height: 30,
                )
              : Image.asset(
                  image,
                  height: 30,
                ),
          Visibility(
            visible: count != null,
            child: Container(
                margin: const EdgeInsets.only(left: 5),
                child: Text(
                  "${count}",
                  style: AppTextStyle.access1(textColor: AppColors.appBlack),
                )),
          )
        ],
      ),
    );
  }
//endregion
}
