import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/post/all_feed/all_feed_bloc.dart';
import 'package:swadesic/features/post/all_feed/all_feed_pagination.dart';
import 'package:swadesic/features/widgets/post_widgets/post_card.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:visibility_detector/visibility_detector.dart';

class AllFeedScreen extends StatefulWidget {
  final ScrollController? parentScrollController;

  const AllFeedScreen({
    Key? key,
    this.parentScrollController,
  }) : super(key: key);

  @override
  State<AllFeedScreen> createState() => _AllFeedScreenState();
}

class _AllFeedScreenState extends State<AllFeedScreen>
    with AutomaticKeepAliveClientMixin<AllFeedScreen> {
  //region Bloc
  late AllFeedBloc allFeedBloc;
  //endregion

  //region Init
  @override
  void initState() {
    super.initState();
    allFeedBloc = AllFeedBloc(context, isDiscoverable: true);

    // If we have a parent scroll controller, use it for pagination
    if (widget.parentScrollController != null) {
      widget.parentScrollController!.addListener(_onParentScroll);
    }

    allFeedBloc.init();
  }

  void _onParentScroll() {
    if (!mounted || widget.parentScrollController == null) return;

    final maxScroll = widget.parentScrollController!.position.maxScrollExtent;
    final currentScroll = widget.parentScrollController!.position.pixels;

    // Trigger pagination when we're close to the bottom
    if (currentScroll >= maxScroll * 0.8) {
      allFeedBloc.allFeedPagination.getPaginationFeeds();
    }
  }

  @override
  void dispose() {
    // Remove parent scroll controller listener if it exists
    if (widget.parentScrollController != null) {
      widget.parentScrollController!.removeListener(_onParentScroll);
    }

    allFeedBloc.dispose();
    super.dispose();
  }
  //endregion

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return body();
  }

  //region Body
  Widget body() {
    return RefreshIndicator(
      color: AppColors.brandBlack,
      onRefresh: () async {
        await allFeedBloc.init();
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            margin: const EdgeInsets.symmetric(vertical: 0),
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
            child: Text(
              AppStrings.whatsOnSwadesic,
              textAlign: TextAlign.start,
              style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
            ),
          ),
          allFeedList(),
        ],
      ),
    );
  }
  //endregion

  //region All Feed List
  Widget allFeedList() {
    // Get reference to the PostDataModel using Provider
    var postDataModel = Provider.of<PostDataModel>(context, listen: true);
    // Get reference to the ProductDataModel using Provider
    var productDataModel = Provider.of<ProductDataModel>(context, listen: true);

    return StreamBuilder<AllFeedState>(
      stream: allFeedBloc.feedStateCtrl.stream,
      initialData: AllFeedState.Loading,
      builder: (context, snapshot) {
        // Loading
        if (snapshot.data == AllFeedState.Loading &&
            allFeedBloc.feedList.isEmpty) {
          return Container(
            alignment: Alignment.center,
            height: MediaQuery.of(context).size.height * 0.3,
            child: AppCommonWidgets.appCircularProgress(),
          );
        }

        // Empty
        if (snapshot.data == AllFeedState.Empty) {
          return Container(
            alignment: Alignment.center,
            height: MediaQuery.of(context).size.height * 0.3,
            child: Text(
              AppStrings.noPostFound,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
          );
        }

        // Failed
        if (snapshot.data == AllFeedState.Failed) {
          return AppCommonWidgets.errorWidget(
            height: MediaQuery.of(context).size.height * 0.3,
            onTap: () {
              allFeedBloc.init();
            },
          );
        }

        // Determine which scroll controller to use
        ScrollController effectiveScrollController =
            widget.parentScrollController ?? allFeedBloc.scrollController;

        // Success - Use different approaches based on whether we have a parent controller
        if (widget.parentScrollController == null) {
          // Standalone mode - use CustomScrollView with SliverList for better performance
          return Flexible(
            child: CustomScrollView(
              controller: effectiveScrollController,
              physics: const AlwaysScrollableScrollPhysics(),
              slivers: [
                _buildSliverList(postDataModel, productDataModel),
                _buildPaginationSliver(),
              ],
            ),
          );
        } else {
          // Embedded mode - use Column with non-scrollable list
          // Wrap in SizedBox to constrain height and prevent layout issues
          return SizedBox(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildNonScrollableList(postDataModel, productDataModel),
                if (allFeedBloc.feedList.isNotEmpty) paginationLoading(),
              ],
            ),
          );
        }
      },
    );
  }

  // Build a SliverList for better performance in standalone mode - exactly like in feed_screen.dart
  Widget _buildSliverList(
      PostDataModel postDataModel, ProductDataModel productDataModel) {
    return Consumer2<PostDataModel, ProductDataModel>(
      builder: (context, postDataModel, productDataModel, child) {
        return SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              if (index == allFeedBloc.feedList.length) {
                return Visibility(
                  visible: allFeedBloc.isPaginationLoading,
                  child: Container(
                    padding: const EdgeInsets.all(8.0),
                    child: Center(
                      child: AppCommonWidgets.appCircularProgress(
                          isPaginationProgress: true),
                    ),
                  ),
                );
              }

              // Safety check for index
              if (index >= allFeedBloc.feedList.length) {
                return const SizedBox();
              }

              final item = allFeedBloc.feedList[index];
              if (item == null) {
                return const SizedBox();
              }

              if (item is PostDetail) {
                // Safety check for postDataModel
                if (postDataModel.allPostDetailList.isEmpty) {
                  return const SizedBox();
                }

                final postDetail = postDataModel.allPostDetailList.firstWhere(
                  (element) =>
                      element.postOrCommentReference != null &&
                      item.postOrCommentReference != null &&
                      element.postOrCommentReference ==
                          item.postOrCommentReference,
                  orElse: () => PostDetail(),
                );

                if (postDetail.postOrCommentReference == null) {
                  return const SizedBox();
                }

                return Padding(
                  padding: const EdgeInsets.only(bottom: 20),
                  child: RepaintBoundary(
                    child: PostCard(
                      key:
                          ValueKey('post_${postDetail.postOrCommentReference}'),
                      postDetail: postDetail,
                      isCustomTitleVisible: true,
                      customTitle: item.contentHeaderText,
                      onTapDelete: () {
                        // Handle delete
                      },
                      onTapDrawer: () {
                        // Handle drawer
                      },
                      onTapEdit: () {
                        // Handle edit
                      },
                      onTapHeart: () {
                        allFeedBloc.onTapHeart(postDetail: postDetail);
                      },
                      onTapShare: () {
                        allFeedBloc.onTapShare(postDetail: postDetail);
                      },
                      onTapProfileImage: () {
                        allFeedBloc.onTapUserOrStoreIcon(
                            reference:
                                postDetail.createdBy!.userOrStoreReference!);
                      },
                      onTapPost: () {
                        allFeedBloc.goToSinglePostView(
                            postReference: postDetail.postOrCommentReference!);
                      },
                    ),
                  ),
                );
              } else if (item is Product) {
                // Safety check for productDataModel
                if (productDataModel.allProducts.isEmpty) {
                  return const SizedBox();
                }

                final product = productDataModel.allProducts.firstWhere(
                  (element) =>
                      element.productReference != null &&
                      item.productReference != null &&
                      element.productReference == item.productReference,
                  orElse: () => Product(),
                );

                if (product.productReference == null) {
                  return const SizedBox();
                }

                return Padding(
                  padding: const EdgeInsets.only(bottom: 20),
                  child: RepaintBoundary(
                    child: ProductDetailFullCard(
                      key: ValueKey('product_${product.productReference}'),
                      product: product,
                      isFromAddProduct: false,
                      isFullView: false,
                      isCustomTitleVisible: true,
                      customTitle: item.contentHeaderText,
                    ),
                  ),
                );
              }

              return const SizedBox();
            },
            childCount: allFeedBloc.feedList.length +
                (allFeedBloc.currentApiCallStatus != AllFeedState.Empty
                    ? 1
                    : 0),
          ),
        );
      },
    );
  }

  // Build a pagination sliver for standalone mode
  Widget _buildPaginationSliver() {
    return SliverToBoxAdapter(
      child: StreamBuilder<AllFeedPaginationState>(
        stream: allFeedBloc.allFeedPagination.feedPaginationStateCtrl.stream,
        initialData: AllFeedPaginationState.Done,
        builder: (context, snapshot) {
          if (snapshot.data == AllFeedPaginationState.Empty) {
            return const SizedBox();
          }

          if (snapshot.data == AllFeedPaginationState.Loading) {
            return VisibilityDetector(
              key: UniqueKey(),
              onVisibilityChanged: (visibilityInfo) {
                var visiblePercentage = visibilityInfo.visibleFraction * 100;
                if (visiblePercentage == 100) {
                  allFeedBloc.allFeedPagination.getPaginationFeeds();
                }
              },
              child: AppCommonWidgets.appCircularProgress(
                  isPaginationProgress: true),
            );
          }

          return const SizedBox();
        },
      ),
    );
  }

  // Build a non-scrollable list for embedded mode - exactly like in feed_screen.dart
  Widget _buildNonScrollableList(
      PostDataModel postDataModel, ProductDataModel productDataModel) {
    // Safety check - if the feed list is empty, return an empty container
    if (allFeedBloc.feedList.isEmpty &&
        allFeedBloc.currentApiCallStatus != AllFeedState.Loading) {
      return Container(
        alignment: Alignment.center,
        height: 100,
        child: Text(
          AppStrings.noPostFound,
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
      );
    }

    return Consumer2<PostDataModel, ProductDataModel>(
      builder: (context, postDataModel, productDataModel, child) {
        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: allFeedBloc.feedList.length +
              (allFeedBloc.currentApiCallStatus != AllFeedState.Empty &&
                      allFeedBloc.isPaginationLoading
                  ? 1
                  : 0),
          itemBuilder: (context, index) {
            if (index == allFeedBloc.feedList.length) {
              return Visibility(
                visible: allFeedBloc.isPaginationLoading,
                child: Container(
                  padding: const EdgeInsets.all(8.0),
                  child: Center(
                    child: AppCommonWidgets.appCircularProgress(
                        isPaginationProgress: true),
                  ),
                ),
              );
            }

            // Safety check for index
            if (index >= allFeedBloc.feedList.length) {
              return const SizedBox();
            }

            final item = allFeedBloc.feedList[index];
            if (item == null) {
              return const SizedBox();
            }

            if (item is PostDetail) {
              // Safety check for postDataModel
              if (postDataModel.allPostDetailList.isEmpty) {
                return const SizedBox();
              }

              final postDetail = postDataModel.allPostDetailList.firstWhere(
                (element) =>
                    element.postOrCommentReference != null &&
                    item.postOrCommentReference != null &&
                    element.postOrCommentReference ==
                        item.postOrCommentReference,
                orElse: () => PostDetail(),
              );

              if (postDetail.postOrCommentReference == null) {
                return const SizedBox();
              }

              return Padding(
                padding: const EdgeInsets.only(bottom: 20),
                child: RepaintBoundary(
                  child: PostCard(
                    key: ValueKey('post_${postDetail.postOrCommentReference}'),
                    postDetail: postDetail,
                    isCustomTitleVisible: true,
                    customTitle: item.contentHeaderText,
                    onTapDelete: () {
                      // Handle delete
                    },
                    onTapDrawer: () {
                      // Handle drawer
                    },
                    onTapEdit: () {
                      // Handle edit
                    },
                    onTapHeart: () {
                      allFeedBloc.onTapHeart(postDetail: postDetail);
                    },
                    onTapShare: () {
                      allFeedBloc.onTapShare(postDetail: postDetail);
                    },
                    onTapProfileImage: () {
                      allFeedBloc.onTapUserOrStoreIcon(
                          reference:
                              postDetail.createdBy!.userOrStoreReference!);
                    },
                    onTapPost: () {
                      allFeedBloc.goToSinglePostView(
                          postReference: postDetail.postOrCommentReference!);
                    },
                  ),
                ),
              );
            } else if (item is Product) {
              // Safety check for productDataModel
              if (productDataModel.allProducts.isEmpty) {
                return const SizedBox();
              }

              final product = productDataModel.allProducts.firstWhere(
                (element) =>
                    element.productReference != null &&
                    item.productReference != null &&
                    element.productReference == item.productReference,
                orElse: () => Product(),
              );

              if (product.productReference == null) {
                return const SizedBox();
              }

              return Padding(
                padding: const EdgeInsets.only(bottom: 20),
                child: RepaintBoundary(
                  child: ProductDetailFullCard(
                    key: ValueKey('product_${product.productReference}'),
                    product: product,
                    isFromAddProduct: false,
                    isFullView: false,
                    isCustomTitleVisible: true,
                    customTitle: item.contentHeaderText,
                  ),
                ),
              );
            }

            return const SizedBox();
          },
        );
      },
    );
  }
  //endregion

  //region Pagination Loading
  Widget paginationLoading() {
    return StreamBuilder<AllFeedPaginationState>(
      stream: allFeedBloc.allFeedPagination.feedPaginationStateCtrl.stream,
      initialData: AllFeedPaginationState.Done,
      builder: (context, snapshot) {
        // Empty
        if (snapshot.data == AllFeedPaginationState.Empty) {
          return const SizedBox();
        }

        // Loading
        if (snapshot.data == AllFeedPaginationState.Loading &&
            allFeedBloc.isPaginationLoading) {
          return VisibilityDetector(
            key: UniqueKey(),
            onVisibilityChanged: (visibilityInfo) {
              var visiblePercentage = visibilityInfo.visibleFraction * 100;
              if (visiblePercentage == 100) {
                allFeedBloc.allFeedPagination.getPaginationFeeds();
              }
            },
            child: Container(
              padding: const EdgeInsets.all(8.0),
              child: Center(
                child: AppCommonWidgets.appCircularProgress(
                    isPaginationProgress: true),
              ),
            ),
          );
        }

        return const SizedBox();
      },
    );
  }
  //endregion
}
