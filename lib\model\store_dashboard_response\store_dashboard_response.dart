class StoreDashboardResponse {
  String? messasge;
  StoreDashBoard? data;

  StoreDashboardResponse({this.messasge, this.data});

  StoreDashboardResponse.fromJson(Map<String, dynamic> json) {
    messasge = json['messasge'];
    data = json['data'] != null ? new StoreDashBoard.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['messasge'] = this.messasge;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class StoreDashBoard {
  String? storeReference;
  bool? trustcenterDetail;
  bool? warrantyAndReturn;
  bool? deliverySettings;
  bool? addProducts;
  bool? openForOrder;
  bool? isActive;
  String? gstNumber;
  int? waitingForConfirmation;
  int? confirmedNotShipped;
  int? delivered;
  int? shippingInProgress;
  int? cancelled;
  int? returnRequested;
  int? returnToPickup;
  int? returned;
  int? refunded;
  int? refundHold;
  bool isCheckListVisible = true;
  double dashboardProgress = 0;
  String? orderAlertMessage;
  String? subscriptionType;
  double? storeValuation;
  String? storeLevel;
  int? analyticsViewCount;
  bool? isVerificationCompleted;
  String? firstVerifiedDate;
  bool? isDiscoverable;

  StoreDashBoard(
      {this.storeReference,
        this.trustcenterDetail,
        this.warrantyAndReturn,
        this.deliverySettings,
        this.addProducts,
        this.openForOrder,
        this.isActive,
        this.gstNumber,
        this.waitingForConfirmation,
        this.confirmedNotShipped,
        this.delivered,
        this.shippingInProgress,
        this.cancelled,
        this.returnRequested,
        this.returnToPickup,
        this.returned,
        this.refunded,
        this.refundHold,
        this.orderAlertMessage,
        this.subscriptionType,
        this.storeValuation,
        this.storeLevel,
        this.analyticsViewCount,
        this.isVerificationCompleted,
        this.firstVerifiedDate,
        this.isDiscoverable
      });

  StoreDashBoard.fromJson(Map<String, dynamic> json) {
    storeReference = json['store_reference'];
    trustcenterDetail = json['trustcenter_detail'];
    warrantyAndReturn = json['warranty_and_return'];
    deliverySettings = json['delivery_settings'];
    addProducts = json['add_products'];
    openForOrder = json['open_for_order'];
    isActive = json['is_active'];
    gstNumber = json['gst_number'];
    waitingForConfirmation = json['waiting_for_confirmation']??0;
    confirmedNotShipped = json['confirmed_not_shipped']??0;
    delivered = json['delivered'];
    shippingInProgress = json['shipping_in_progress'];
    cancelled = json['cancelled'];
    returnRequested = json['return_requested'];
    returnToPickup = json['return_to_pickup'];
    returned = json['returned'];
    refunded = json['refunded'];
    refundHold = json['refund_hold'];
    orderAlertMessage = json['order_alert_message'];
    subscriptionType = json['subscription_type'];
    storeValuation = json['store_valuation'];
    storeLevel = json['store_level'];
    analyticsViewCount = json['analytics_view_count'];
    isVerificationCompleted = json['is_verification_completed'];
    firstVerifiedDate = json['first_verified_date'];
    isDiscoverable = json['is_discoverable'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['store_reference'] = this.storeReference;
    data['trustcenter_detail'] = this.trustcenterDetail;
    data['warranty_and_return'] = this.warrantyAndReturn;
    data['delivery_settings'] = this.deliverySettings;
    data['add_products'] = this.addProducts;
    data['open_for_order'] = this.openForOrder;
    data['is_active'] = this.isActive;
    data['gst_number'] = this.gstNumber;
    data['waiting_for_confirmation'] = this.waitingForConfirmation;
    data['confirmed_not_shipped'] = this.confirmedNotShipped;
    data['delivered'] = this.delivered;
    data['shipping_in_progress'] = this.shippingInProgress;
    data['cancelled'] = this.cancelled;
    data['return_requested'] = this.returnRequested;
    data['return_to_pickup'] = this.returnToPickup;
    data['returned'] = this.returned;
    data['refunded'] = this.refunded;
    data['refund_hold'] = this.refundHold;
    data['order_alert_message'] = this.orderAlertMessage;
    data['subscription_type'] = this.subscriptionType;
    data['store_valuation'] = this.storeValuation;
    data['store_level'] = this.storeLevel;
    data['analytics_view_count'] = this.analyticsViewCount;
    data['is_verification_completed'] = this.isVerificationCompleted;
    data['first_verified_date'] = this.firstVerifiedDate;
    data['is_discoverable'] = this.isDiscoverable;
    return data;
  }
}
