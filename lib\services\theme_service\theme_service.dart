import 'package:flutter/material.dart';
import 'package:swadesic/services/cache_storage/cache_storage_service.dart';
import 'package:swadesic/services/cache_storage/storage_keys.dart';
import 'package:swadesic/util/theme_mode.dart';

class ThemeService {
  static final ThemeService _instance = ThemeService._internal();
  factory ThemeService() => _instance;
  ThemeService._internal();

  final CacheStorageService _cacheStorageService = CacheStorageService();

  /// Get the saved theme mode from storage
  Future<AppThemeMode> getThemeMode() async {
    try {
      final String savedTheme = await _cacheStorageService.getString(StorageKeys.themeMode);
      if (savedTheme.isNotEmpty) {
        return AppThemeModeExtension.fromString(savedTheme);
      }
    } catch (e) {
      debugPrint('Error getting theme mode: $e');
    }
    return AppThemeMode.system; // Default to system theme
  }

  /// Save the theme mode to storage
  Future<void> setThemeMode(AppThemeMode themeMode) async {
    try {
      await _cacheStorageService.saveString(StorageKeys.themeMode, themeMode.value);
    } catch (e) {
      debugPrint('Error saving theme mode: $e');
    }
  }

  /// Get the actual theme mode based on system settings
  ThemeMode getActualThemeMode(AppThemeMode appThemeMode, BuildContext context) {
    switch (appThemeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }

  /// Check if the current theme is dark
  bool isDarkMode(BuildContext context, AppThemeMode appThemeMode) {
    switch (appThemeMode) {
      case AppThemeMode.light:
        return false;
      case AppThemeMode.dark:
        return true;
      case AppThemeMode.system:
        return MediaQuery.of(context).platformBrightness == Brightness.dark;
    }
  }
}
