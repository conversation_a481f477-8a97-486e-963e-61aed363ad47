

//happy
import 'package:swadesic/model/store_dashboard_response/store_dashboard_response.dart';
import 'package:swadesic/model/store_info/store_config.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';

class StoreDashboardService {
  // region Common Variables
  late HttpService httpService;

  // endregion

  // region | Constructor |
  StoreDashboardService() {
    httpService = HttpService();
  }

  // endregion


  // region Get store dashboard
  Future<StoreDashboardResponse>getStoreDashboard({required String storeReference}) async {
    Map<String, dynamic> response;
    var url = "${AppConstants.getStoreDashboard}$storeReference/";
    //#region Region - Execute Request
    response = await httpService.getApiCall(url);
    // return response;
    return StoreDashboardResponse.fromJson(response);
  }
// endregion



  // region Open close store
  openCloseStore({required String storeReference}) async {
    Map<String, dynamic> response;
    var url = "${AppConstants.storeOpenClose}$storeReference/";
    //#region Region - Execute Request
    response = await httpService.getApiCall(url);
    // return response;
    // return StoreOpenCloseResponse.fromJson(response);
  }
// endregion


  // region Delete store
  deleteStore({required String storeReference}) async {
    Map<String, dynamic> response;
    var url = "${AppConstants.deleteStore}$storeReference/";
    //#region Region - Execute Request
    response = await httpService.patchApi({},url);
    //print(response);
    // return response;
    // return StoreOpenCloseResponse.fromJson(response);
  }
// endregion


  // region Active and deactivate store
  activeAndDeActiveStore({required String storeReference,required bool storeActiveStatus}) async {
    var body = {
    "is_active": storeActiveStatus
    };
    Map<String, dynamic> response;
    var url = "${AppConstants.activeDeactiveStore}$storeReference/";
    //#region Region - Execute Request
    response = await httpService.patchApi(body,url);

    //print(body);
    //print(response);
    // return response;
    // return StoreOpenCloseResponse.fromJson(response);
  }
// endregion


// region Get store config
  Future<StoreConfig> getStoreConfig({required String storeReference}) async {
    Map<String, dynamic> response;
    var url = "${AppConstants.getStoreConfig}$storeReference/";
    //#region Region - Execute Request
    response = await httpService.getApiCall(url);
    return StoreConfig.fromJson(response['data']);
  }
// endregion

  // region Request early public discovery unlock
  Future<Map<String, dynamic>> requestPublicDiscovery({required String storeReference}) async {
    var body = {
      "reference": storeReference,
      "application_type": "PUBLIC_DISCOVERY_REQUEST",
      "details": []
    };
    var url = AppConstants.requestPublicDiscovery;
    Map<String, dynamic> response = await httpService.postApiCall(body, url);
    return response;
  }
  // endregion

}
