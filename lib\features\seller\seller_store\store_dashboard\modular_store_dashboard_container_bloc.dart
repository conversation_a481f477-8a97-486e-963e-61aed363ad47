import 'dart:async';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/complete_check_list/complete_check_list.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/mini_dashboard_info/mini_dashboard_info.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/modular_store_dashboard_container.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_dashboard_bloc.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_valuation_card_overlay.dart';
import 'package:swadesic/features/widgets/level_badge/level_badge.dart';
import 'package:swadesic/model/store_dashboard_response/store_dashboard_response.dart';
import 'package:swadesic/services/store_dashboard_services/store_dashboard_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';

class ModularStoreDashboardContainerBloc {
  final BuildContext context;
  final String storeReference;
  final StoreDashboardService _dashboardService = StoreDashboardService();
  
  // State
  StoreDashBoard? _storeDashboard;
  bool _isLoading = true;

  // Controllers
  final StreamController<ModularDashboardState> stateController =
      StreamController<ModularDashboardState>.broadcast();

  ModularStoreDashboardContainerBloc(this.context, this.storeReference);

  // Getters
  StoreDashBoard? get storeDashboard => _storeDashboard;
  bool get isLoading => _isLoading;

  Future<void> init() async {
    try {
      stateController.sink.add(ModularDashboardState.loading);
      await _fetchStoreDashboard();
      stateController.sink.add(ModularDashboardState.success);
    } catch (e) {
      stateController.sink.add(ModularDashboardState.error);
    }
  }

  Future<void> _fetchStoreDashboard() async {
    try {
      _isLoading = true;
      final response = await _dashboardService.getStoreDashboard(
        storeReference: storeReference,
      );
      
      _storeDashboard = response.data;
      
      // Update the StoreDashboardDataModel for other widgets
      final storeDashboardDataModel = 
          Provider.of<StoreDashboardDataModel>(context, listen: false);
      storeDashboardDataModel.addDashboard(data: _storeDashboard!);
      
    } catch (e) {
      throw Exception('Failed to load store dashboard');
    } finally {
      _isLoading = false;
    }
  }

  void dispose() {
    stateController.close();
  }

  // Determine which flow to show based on store state
  DashboardFlowType determineFlowType(StoreDashBoard storeDashBoard) {
    // If store is not active, show onboarding flow
    if (!storeDashBoard.isActive!) {
      return DashboardFlowType.onboarding;
    }

    if (!storeDashBoard.openForOrder!) {
      return DashboardFlowType.onboarding;
    }

    // If store is active but onboarding is not complete, show onboarding flow
    if (storeDashBoard.dashboardProgress < 100) {
      return DashboardFlowType.onboarding;
    }

    // Show normal view for fully activated and completed stores
    return DashboardFlowType.normalView;
  }

  // Determine which onboarding step to show
  OnboardingStep determineOnboardingStep(StoreDashBoard storeDashBoard) {
    // If dashboard progress is less than 100, show finish setup
    if (storeDashBoard.dashboardProgress < 100) {
      return OnboardingStep.finishSetup;
    }

    // If progress is 100 but store is not active, show verification in progress
    if (storeDashBoard.dashboardProgress >= 100 && !storeDashBoard.isActive!) {
      return OnboardingStep.verificationInProgress;
    }

    // If store is active but this is the first time, show get orders
    return OnboardingStep.getOrders;
  }

  // Build finish setup widget
  Widget buildFinishSetupWidget(StoreDashBoard storeDashBoard) {
    return GestureDetector(
      onTap: () => _showCompleteCheckList(storeDashBoard),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        "Finish Setup & Start Sharing",
                        style: AppTextStyle.contentHeading0(
                            textColor: AppColors.appBlack),
                      ),
                    ),
                    _buildDotsIndicator(0),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        "Complete your store so people with your StoreLink can see it instantly.",
                        style:
                            AppTextStyle.smallTextRegular(textColor: AppColors.appBlack),
                      ),
                    ),
                    // Progress indicator
                    Padding(
                      padding:
                          const EdgeInsets.symmetric(horizontal: 15),
                      child: CircularPercentIndicator(
                        radius: 20.0,
                        lineWidth: 5.0,
                        percent:
                            storeDashBoard.dashboardProgress / 100,
                        backgroundColor:
                            AppColors.brandBlack.withOpacity(0.1),
                        center: appText(
                          "${storeDashBoard.dashboardProgress.round()}%",
                          color: AppColors.appBlack,
                          maxLine: 1,
                          fontFamily: AppConstants.rRegular,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                        progressColor: AppColors.brandBlack,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build verification in progress widget
  Widget buildVerificationInProgressWidget() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      "Verification in Progress",
                      style:
                          AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                    ),
                  ),
                  _buildDotsIndicator(0),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  // Clock icon
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.lightGray,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.access_time,
                      color: AppColors.appBlack,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      "We're reviewing your documents. In the meantime, add products and request reviews to get ready for orders.",
                      style: AppTextStyle.smallTextRegular(textColor: AppColors.appBlack),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Build get orders widget
  Widget buildGetOrdersWidget(StoreDashBoard storeDashBoard) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      "Get orders on Swadesic",
                      style:
                          AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                    ),
                  ),
                  _buildDotsIndicator(0),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      "Open your store for orders. You can toggle this as many times you want in settings.",
                      style: AppTextStyle.smallTextRegular(textColor: AppColors.appBlack),
                    ),
                  ),
                  // Toggle switch
                  SizedBox(
                    height: 21,
                    width: 43.24,
                    child: FlutterSwitch(
                      width: 43.24,
                      height: 21.0,
                      toggleSize: 21,
                      borderRadius: 21.0,
                      padding: 0.0,
                      activeColor: AppColors.lightGray,
                      inactiveColor: AppColors.lightGray,
                      toggleColor: storeDashBoard.openForOrder!
                          ? AppColors.brandBlack
                          : AppColors.darkStroke,
                      value: storeDashBoard.openForOrder!,
                      onToggle: (value) => _toggleStoreOpenClose(),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Build dots indicator
  Widget _buildDotsIndicator(int activeIndex) {
    return Row(
      children: List.generate(4, (index) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 2),
          width: 6,
          height: 6,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color:
                index == activeIndex ? AppColors.appBlack : AppColors.lightGray,
          ),
        );
      }),
    );
  }

  // Show complete checklist bottom sheet
  void _showCompleteCheckList(StoreDashBoard storeDashBoard) {
    CommonMethods.appMinimumBottomSheets(
      bottomSheetName: AppStrings.completeStoreActivation,
      screen: CompleteCheckList(
        storeDashBoard: storeDashBoard,
        previousScreenContext: context,
        storeId: AppConstants.appData.storeId!,
      ),
      context: context,
    ).then((value) {
      // Refresh data after bottom sheet closes
      // You might want to call a refresh method here
    });
  }

  // Toggle store open/close
  Future<void> _toggleStoreOpenClose() async {
    final storeDashboardDataModel =
        Provider.of<StoreDashboardDataModel>(context, listen: false);
    bool previousState = storeDashboardDataModel.storeDashBoard.openForOrder!;
    
    try {
      // Optimistically update UI
      storeDashboardDataModel.storeDashBoard.openForOrder = !previousState;
      storeDashboardDataModel.updateUi();

      // Call API to update store status
      await StoreDashboardService().openCloseStore(storeReference: storeReference);
      
      // Refresh dashboard data after successful API call
      await _fetchStoreDashboard();
      
      // Show success message
      CommonMethods.toastMessage(
        storeDashboardDataModel.storeDashBoard.openForOrder!
            ? AppStrings.yourStoreIsCurrentlyOpen
            : AppStrings.yourStoreIsCurrentlyClosed,
        context,
      );
    } catch (error) {
      // Revert UI on error
      storeDashboardDataModel.storeDashBoard.openForOrder = previousState;
      storeDashboardDataModel.updateUi();
      
      // Show error message
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    }
  }

  // Build mini dashboard widget (normal view)
  Widget buildMiniDashboardWidget(StoreDashBoard storeDashBoard) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  "Orders and their statuses",
                  style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack),
                ),
              ),
              _buildDotsIndicator(0),
            ],
          ),
          const MiniDashboard(),
        ],
      ),
    );
  }

  // Build public discovery widget (normal view)
  Widget buildPublicDiscoveryWidget() {
    return GestureDetector(
      onTap: () => _showPublicDiscoveryBottomSheet(),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          "Unlock Public Discovery",
                          style: AppTextStyle.contentHeading0(
                              textColor: AppColors.appBlack),
                        ),
                      ),
                      _buildDotsIndicator(1),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          "Get visibility & orders beyond your network with public discovery via feed, search, recommendations etc., ",
                          style: AppTextStyle.smallTextRegular(
                              textColor: AppColors.appBlack),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 3,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.all(5),
                        child: Icon(Icons.arrow_forward, color: AppColors.appBlack),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
  }


  // Build public discovery widget (normal view)
  Widget buildMakeMostOutofSwadesicWidget() {
    return GestureDetector(
      onTap: () => _showMakeMostOutofSwadesicBottomSheet(),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          "Make the Most of Swadesic",
                          style: AppTextStyle.contentHeading0(
                              textColor: AppColors.appBlack),
                        ),
                      ),
                      _buildDotsIndicator(2),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          "See all the ways Swadesic can work for you — explore every feature and benefit for your store.",
                          style: AppTextStyle.smallTextRegular(
                              textColor: AppColors.appBlack),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 3,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.all(5),
                        child: Icon(Icons.arrow_forward, color: AppColors.appBlack),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
  }



  // Build store valuation widget (normal view)
  Widget buildStoreValuationWidget() {
    return GestureDetector(
      onTap: () => _showStoreValuationOverlay(),
      child: Consumer<SellerOwnStoreInfoDataModel>(
        builder: (context, storeInfoModel, child) {
          final storeValuation =
              storeInfoModel.storeInfo?.storeValuation ?? 1200000;

          final storeLevel = int.tryParse(storeInfoModel.storeInfo?.storeLevel?.toString() ?? '1') ?? 1;
          // Level ranges mapping (dummy data)
          final Map<int, Map<String, double>> levelRanges = {
            1: {'min': 0, 'max': 100000},
            2: {'min': 100000, 'max': 500000},
            3: {'min': 500000, 'max': 1000000},
            4: {'min': 1000000, 'max': 2000000},
            5: {'min': 2000000, 'max': 5000000},
            6: {'min': 5000000, 'max': 10000000},
            7: {'min': 10000000, 'max': 20000000},
            8: {'min': 20000000, 'max': 50000000},
            9: {'min': 50000000, 'max': 50000000}, // Max level
          };

          double progress = 0.0;

          if (storeLevel < 9) {
            final currentRange = levelRanges[storeLevel]!;
            final nextRange = levelRanges[storeLevel + 1]!;
            final currentMin = currentRange['min']!;
          final nextMin = nextRange['min']!;

          progress = (storeValuation - currentMin) / (nextMin - currentMin);
          progress = progress.clamp(0.0, 1.0);
          } else {
            progress = 1.0;
          }

          return Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Row(
                        children: [
                          Text(
                            "Store valuation @ ",
                            style: AppTextStyle.contentHeading0(
                                textColor: AppColors.appBlack),
                          ),
                          Text(
                            "₹${storeValuation.toStringAsFixed(0)}",
                            style: AppTextStyle.contentHeading0(
                                textColor: AppColors.appBlack),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            "₹200 ▲",
                            style: AppTextStyle.access1(textColor: Colors.red),
                          ),
                        ],
                      ),
                    ),
                    _buildDotsIndicator(3),
                  ],
                ),
                const SizedBox(height: 8),
                // Progress bar
                Stack(
                  children: [
                    Container(
                      width: double.infinity,
                      height: 12,
                      decoration: BoxDecoration(
                        color: AppColors.appWhite,
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: progress,
                      child: Container(
                        height: 12,
                        decoration: BoxDecoration(
                          color: AppColors.brandBlack,
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                    ),
                    // Circular indicator at the end of progress
                    Positioned(
                      left: (MediaQuery.of(context).size.width - 44) * progress,
                      top: 0.5,
                      child: Container(
                        width: 11,
                        height: 11,
                        decoration: BoxDecoration(
                          color: AppColors.appWhite,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: AppColors.brandBlack,
                            width: 2,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      DateFormat('MMMM d, yyyy').format(DateTime.now()),
                      style:
                          AppTextStyle.smallText(textColor: AppColors.appBlack),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          storeLevel < 9
                              ? "₹${(levelRanges[storeLevel + 1]!['min']!).toStringAsFixed(0)} to "
                              : "Max Level",
                          style: AppTextStyle.smallText2(
                              textColor: AppColors.writingBlack0),
                        ),
                        LevelBadge.createLevelBadge(
                          level: (storeLevel + 1).toString(),
                          badgeType: LevelBadgeType.store,
                          width: 28,
                          height: 28,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // Helper method to build order status items
  Widget _buildOrderStatusItem(String imagePath, int count) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        imagePath.endsWith('svg')
            ? SvgPicture.asset(imagePath, height: 24, color: AppColors.appBlack)
            : Image.asset(imagePath, height: 24),
        const SizedBox(width: 4),
        Text(
          count.toString(),
          style: AppTextStyle.access1(textColor: AppColors.appBlack),
        ),
      ],
    );
  }

  // Show public discovery bottom sheet – updated with full content
  void _showPublicDiscoveryBottomSheet() {
    CommonMethods.appBottomSheet(
      bottomSheetName: "Public Discovery",
      screen: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Main Heading
              Text(
                "Unlock Public Discovery",
                style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),

              // One-sentence benefit statement
              Text(
                "Get visibility beyond your network – appear in Swadesic’s feed, search, and recommendations to reach new buyers nationwide.",
                style: AppTextStyle.contentText1(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 16),

              // Why not in public discovery yet
              Text(
                "Why Your Store Isn’t in Public Discovery Yet",
                style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),
              Text(
                "Swadesic keeps Public Discovery clean and valuable for both buyers and sellers.",
                style: AppTextStyle.contentText1(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),
              Text(
                "Only stores that show credibility and real activity are listed publicly. This way we avoid low-quality stores or random fake stores on the community so buyers always see active, trusted stores, and sellers get higher-quality visibility.",
                style: AppTextStyle.contentText1(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),
              Text(
                "Your store is in Private Discovery mode as you just started out and yet to build credibility on Swadesic.",
                style: AppTextStyle.contentText1(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),
              Text(
                "A store in private mode is only visible to people who have a store/product link.",
                style: AppTextStyle.contentText1(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 16),

              // How to unlock heading
              Text(
                "How to Unlock Public Discovery",
                style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),

              // Numbered list 1-4
              _buildNumberedItem(1, "Complete your store with quality products and content."),
              _buildNumberedItem(2, "Share your store link with existing customers in your network. Invite them to support your store."),
              _buildNumberedItem(3, "Receive orders and engagement from your network – let them like your product, comments, posts etc."),
              _buildNumberedItem(4, "Engage Customers – request reviews, build credibility."),
              const SizedBox(height: 16),

              // Requirements heading
              Text(
                "Requirements",
                style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),

              // Bullet list for requirements
              _buildBulletedItem("20+ supporters"),
              _buildBulletedItem("5 orders"),
              _buildBulletedItem("10 likes"),
              _buildBulletedItem("3 reviews (external or internal reviews)"),

              const SizedBox(height: 24),

              // Early unlock section
              Text(
                "Already Have a Big Audience?",
                style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),
              Text(
                "If you already have a large audience outside Swadesic, you can request an early unlock:",
                style: AppTextStyle.contentText1(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 8),
              _buildBulletedItem("Have 10,000+ followers on any social media platform"),
              _buildBulletedItem("Add your Swadesic StoreLink to your social media bio"),
              _buildBulletedItem("Submit your application for review"),
              const SizedBox(height: 16),

              // CTA button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.brandBlack,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 14),
                  ),
                  onPressed: () async {
                    try {
                      await _dashboardService.requestPublicDiscovery(storeReference: storeReference);
                      CommonMethods.toastMessage("Request submitted successfully", context);
                      Navigator.of(context).pop();
                    } catch (e) {
                      CommonMethods.toastMessage(e.toString(), context);
                    }
                  },
                  child: Text(
                    "Request for Public Discovery →",
                    style: AppTextStyle.access1(textColor: AppColors.appWhite),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      context: context,
    );
  }

  // Helper widget for numbered list items within bottom sheet
  Widget _buildNumberedItem(int number, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "$number.",
            style: AppTextStyle.contentText1(textColor: AppColors.appBlack),
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              text,
              style: AppTextStyle.contentText1(textColor: AppColors.appBlack),
            ),
          ),
        ],
      ),
    );
  }

  // Helper widget for bulleted list items within bottom sheet
  Widget _buildBulletedItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text("•", style: TextStyle(fontSize: 14, height: 1.4)),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              text,
              style: AppTextStyle.contentText1(textColor: AppColors.appBlack),
            ),
          ),
        ],
      ),
    );
  }

    // Show public discovery bottom sheet
  void _showMakeMostOutofSwadesicBottomSheet() {
    CommonMethods.appMinimumBottomSheets(
      bottomSheetName: "Make Most Out of Swadesic",
      screen: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Top 10 Must-Do for Swadesic Store Owners",
                style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 12),
              _buildNumberedItem(1, "Complete store profile (logo, cover, bio, accurate location)."),
              _buildNumberedItem(2, "List at least 10–15 SKUs with high-quality images & benefit-driven descriptions."),
              _buildNumberedItem(3, "Tag products correctly and add variants where possible."),
              _buildNumberedItem(4, "Invite your existing network to follow your store."),
              _buildNumberedItem(5, "Share your store link on all social media, WhatsApp, and offline signage."),
              _buildNumberedItem(6, "Grow community by adding more supporters inside Swadesic."),
              _buildNumberedItem(7, "Use product codes for easy sharing with buyers."),
              _buildNumberedItem(8, "Tag products in posts and post regular updates in store feed."),
              _buildNumberedItem(9, "Request reviews from past buyers & gather reviews for Swadesic orders."),
              _buildNumberedItem(10, "Respond to orders instantly and keep store active with fresh listings."),
            ],
          ),
        ),
      ),
      context: context,
    );
  }

  // Show store valuation overlay
  void _showStoreValuationOverlay() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return const StoreValuationCardOverlay();
      },
    );
  }
}
